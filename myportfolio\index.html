<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
    <title><PERSON> - <PERSON>folio</title>
    <meta name="description" content="Full-Stack Developer & Software Engineer specializing in modern web technologies" />
    <link rel="icon" type="image/png" href="/images/logo-removebg-preview.png" />

    <!-- Mobile optimization meta tags -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="theme-color" content="#000000" />
    <meta name="msapplication-TileColor" content="#000000" />
    <meta name="format-detection" content="telephone=no" />

    <!-- Preload critical resources -->
    <link rel="preload" href="/images/abm.png" as="image" />
    <link rel="preload" href="/images/logo-removebg-preview.png" as="image" />

    <!-- SEO Meta Tags -->
    <meta name="author" content="Abdul Basit Memon" />
    <meta name="keywords" content="Full-Stack Developer, Software Engineer, React, Node.js, TypeScript, Web Development" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Abdul Basit Memon - Portfolio" />
    <meta property="og:description" content="Full-Stack Developer & Software Engineer specializing in modern web technologies" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/images/abm.png" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Abdul Basit Memon - Portfolio" />
    <meta name="twitter:description" content="Full-Stack Developer & Software Engineer specializing in modern web technologies" />
    <meta name="twitter:image" content="/images/abm.png" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
