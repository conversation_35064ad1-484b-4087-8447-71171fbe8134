import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { initPerformanceOptimizations } from "@/utils/smoothScroll";
import { preloadCriticalImages } from "@/components/OptimizedImage";
import { useEffect } from 'react';
import Home from "@/pages/Home";
import Prologware from "@/pages/Prologware";
import Projects from "@/pages/Projects";
import NotFound from "@/pages/not-found";

// Include Font Awesome with performance optimization
const link = document.createElement('link') as HTMLLinkElement;
link.rel = 'preload';
link.as = 'style';
link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
link.onload = function() { (this as HTMLLinkElement).rel = 'stylesheet'; };
document.head.appendChild(link);

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/projects" component={Projects} />
      <Route path="/prologware" component={Prologware} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  useEffect(() => {
    // Initialize performance optimizations on app load
    initPerformanceOptimizations();

    // Preload critical images
    preloadCriticalImages([
      '/src/assets/abm.png',
      '/src/assets/logo-removebg-preview.png',
      '/src/assets/Resume.pdf'
    ]);

    // Add smooth scrolling to root element
    document.documentElement.classList.add('smooth-scroll');

    // Add viewport meta tag for better mobile experience
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes');
    }

    return () => {
      document.documentElement.classList.remove('smooth-scroll');
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <div className="min-h-screen">
          <Toaster />
          <Router />
        </div>
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
