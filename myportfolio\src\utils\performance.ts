// Performance optimization utilities

// Lazy loading for images
export const lazyLoadImage = (img: HTMLImageElement, src: string) => {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const lazyImage = entry.target as HTMLImageElement;
          lazyImage.src = src;
          lazyImage.classList.add('loaded');
          lazyImage.classList.remove('lazy-load');
          imageObserver.unobserve(lazyImage);
        }
      });
    });

    img.classList.add('lazy-load');
    imageObserver.observe(img);
  } else {
    // Fallback for older browsers
    img.src = src;
    img.classList.add('loaded');
  }
};

// Preload critical images
export const preloadImages = (imageSrcs: string[]) => {
  imageSrcs.forEach(src => {
    const img = new Image();
    img.src = src;
  });
};

// Debounce function for performance
export const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Smooth scroll to element
export const smoothScrollTo = (elementId: string) => {
  const element = document.getElementById(elementId);
  if (element) {
    const headerOffset = 80;
    const elementPosition = element.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    });
  }
};

// Throttle function for scroll events
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Enhanced preload function with error handling
export const preloadResource = (href: string, as: string, crossorigin?: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (crossorigin) link.crossOrigin = crossorigin;

    link.onload = () => resolve();
    link.onerror = () => reject(new Error(`Failed to preload ${href}`));

    document.head.appendChild(link);
  });
};

// Performance monitoring
export const measurePerformance = (name: string, fn: () => void): void => {
  if ('performance' in window && 'mark' in performance) {
    performance.mark(`${name}-start`);
    fn();
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);

    const measure = performance.getEntriesByName(name)[0];
    console.log(`${name} took ${measure.duration.toFixed(2)} milliseconds`);
  } else {
    fn();
  }
};

// Optimize animations with requestAnimationFrame
export const optimizedAnimation = (callback: () => void): number => {
  return requestAnimationFrame(callback);
};

// Cancel optimized animation
export const cancelOptimizedAnimation = (id: number): void => {
  cancelAnimationFrame(id);
};

// Device-specific optimizations
export const getDeviceOptimizations = () => {
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  const isLowEndDevice = navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2;
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

  return {
    isMobile,
    isLowEndDevice,
    prefersReducedMotion,
    shouldReduceAnimations: isLowEndDevice || prefersReducedMotion,
    shouldOptimizeImages: isMobile || isLowEndDevice,
  };
};