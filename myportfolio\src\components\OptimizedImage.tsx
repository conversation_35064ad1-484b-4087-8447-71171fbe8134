import { useState, useRef, useEffect } from 'react';
import { useIntersectionObserver } from '@/hooks/use-intersection-observer';
import { useResponsive } from '@/hooks/use-mobile';
import { getDeviceOptimizations } from '@/utils/performance';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  priority?: boolean;
  placeholder?: string;
  sizes?: string;
  quality?: number;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
}

export function OptimizedImage({
  src,
  alt,
  className = '',
  width,
  height,
  priority = false,
  placeholder,
  sizes,
  quality = 85,
  loading = 'lazy',
  onLoad,
  onError,
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(placeholder || '');
  const imgRef = useRef<HTMLImageElement>(null);
  const { ref: intersectionRef, isVisible } = useIntersectionObserver({
    threshold: 0.1,
    rootMargin: '50px',
    triggerOnce: true,
  });
  const { isMobile, width: screenWidth } = useResponsive();
  const { shouldOptimizeImages } = getDeviceOptimizations();

  // Generate responsive image sources
  const generateResponsiveSrc = (baseSrc: string, targetWidth?: number) => {
    if (!shouldOptimizeImages) return baseSrc;
    
    // For demo purposes, return the original src
    // In a real app, you'd generate different sizes
    return baseSrc;
  };

  // Determine optimal image size based on screen size
  const getOptimalSize = () => {
    if (!width || !height) return { width: undefined, height: undefined };
    
    const maxWidth = isMobile ? Math.min(screenWidth - 32, 400) : width;
    const aspectRatio = height / width;
    const optimalHeight = maxWidth * aspectRatio;
    
    return {
      width: Math.round(maxWidth),
      height: Math.round(optimalHeight),
    };
  };

  const { width: optimalWidth, height: optimalHeight } = getOptimalSize();

  useEffect(() => {
    if (priority || (isVisible && loading === 'lazy')) {
      const img = new Image();
      const optimizedSrc = generateResponsiveSrc(src, optimalWidth);
      
      img.onload = () => {
        setCurrentSrc(optimizedSrc);
        setIsLoaded(true);
        onLoad?.();
      };
      
      img.onerror = () => {
        setHasError(true);
        onError?.();
      };
      
      img.src = optimizedSrc;
    }
  }, [src, isVisible, priority, loading, optimalWidth, onLoad, onError]);

  // Combine refs
  const setRefs = (element: HTMLImageElement | null) => {
    imgRef.current = element;
    intersectionRef.current = element;
  };

  if (hasError) {
    return (
      <div 
        className={`bg-gray-200 flex items-center justify-center ${className}`}
        style={{ width: optimalWidth, height: optimalHeight }}
      >
        <span className="text-gray-500 text-sm">Failed to load image</span>
      </div>
    );
  }

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Placeholder/Loading state */}
      {!isLoaded && (
        <div 
          className="absolute inset-0 bg-gray-200 animate-pulse"
          style={{ width: optimalWidth, height: optimalHeight }}
        />
      )}
      
      {/* Main image */}
      <img
        ref={setRefs}
        src={currentSrc}
        alt={alt}
        width={optimalWidth}
        height={optimalHeight}
        sizes={sizes}
        loading={loading}
        decoding="async"
        className={`
          transition-opacity duration-300 
          ${isLoaded ? 'opacity-100' : 'opacity-0'}
          ${className}
        `}
        style={{
          width: optimalWidth ? `${optimalWidth}px` : 'auto',
          height: optimalHeight ? `${optimalHeight}px` : 'auto',
        }}
      />
      
      {/* Loading indicator */}
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        </div>
      )}
    </div>
  );
}

// Higher-order component for automatic optimization
export function withImageOptimization<T extends { src: string; alt: string }>(
  Component: React.ComponentType<T>
) {
  return function OptimizedComponent(props: T) {
    const { shouldOptimizeImages } = getDeviceOptimizations();
    
    if (shouldOptimizeImages) {
      return <OptimizedImage {...props} />;
    }
    
    return <Component {...props} />;
  };
}

// Preload critical images
export function preloadCriticalImages(imageSrcs: string[]) {
  imageSrcs.forEach((src) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  });
}
