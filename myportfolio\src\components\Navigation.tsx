import { useState, useEffect } from "react";
import { Link, useLocation } from "wouter";
import { smoothScrollToElement } from "@/utils/smoothScroll";
import { useResponsive, useDeviceDetection } from "@/hooks/use-mobile";
import { throttle } from "@/utils/performance";

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [location] = useLocation();
  const { isMobile, isTablet } = useResponsive();
  const { isTouchDevice, supportsHover } = useDeviceDetection();

  useEffect(() => {
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        setIsOpen(false);
      }
    };

    document.addEventListener("keydown", handleKeydown);
    return () => document.removeEventListener("keydown", handleKeydown);
  }, []);

  // Enhanced scroll detection with throttling
  useEffect(() => {
    const handleScroll = throttle(() => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      setIsScrolled(scrollTop > 50);
    }, 16); // ~60fps

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Close mobile menu when clicking outside or on mobile
  useEffect(() => {
    if (isOpen) {
      const handleClickOutside = (e: MouseEvent | TouchEvent) => {
        const target = e.target as Element;
        if (!target.closest('.mobile-menu-container') && !target.closest('.mobile-menu-button')) {
          setIsOpen(false);
        }
      };

      // Add both mouse and touch events for better mobile support
      document.addEventListener("click", handleClickOutside);
      document.addEventListener("touchstart", handleClickOutside);

      return () => {
        document.removeEventListener("click", handleClickOutside);
        document.removeEventListener("touchstart", handleClickOutside);
      };
    }
  }, [isOpen]);

  // Close menu when screen size changes to desktop (768px and above)
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768 && isOpen) {
        setIsOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isOpen]);

  // Ensure mobile menu is closed on desktop screens
  useEffect(() => {
    if (window.innerWidth >= 768) {
      setIsOpen(false);
    }
  }, []);

  const scrollToSection = (href: string) => {
    if (location === "/" && href.startsWith("#")) {
      const sectionId = href.substring(1);
      smoothScrollToElement(sectionId, {
        duration: 600,
        offset: 80,
      });
    }
    setIsOpen(false);
  };

  const handleLinkClick = (href: string) => {
    setIsOpen(false); // Always close menu when link is clicked
    if (href.startsWith("#")) {
      setTimeout(() => scrollToSection(href), 100); // Small delay for smooth closing
    }
  };

  const toggleMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(prev => !prev);
  };

  return (
    <>
      {/* Simple Minimalist Navbar */}
      <nav className="navbar fixed w-full top-0 z-50 bg-[#171723]/95 backdrop-blur-md border-b border-white/10">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6">
          <div className="flex items-center justify-between h-10 sm:h-12 md:h-14">
            {/* Brand */}
            <Link href="/">
              <span className="text-white font-medium text-lg tracking-wide cursor-pointer">
                ABM
              </span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              {location === "/" ? (
                <>
                  <button
                    onClick={() => scrollToSection("#home")}
                    className="nav-link"
                  >
                    Home
                  </button>
                  <button
                    onClick={() => scrollToSection("#about")}
                    className="nav-link"
                  >
                    About
                  </button>
                  <button
                    onClick={() => scrollToSection("#projects")}
                    className="nav-link"
                  >
                    Projects
                  </button>
                  <button
                    onClick={() => scrollToSection("#services")}
                    className="nav-link"
                  >
                    Services
                  </button>
                  <button
                    onClick={() => scrollToSection("#skills")}
                    className="nav-link"
                  >
                    Core Skills
                  </button>
                  <button
                    onClick={() => scrollToSection("#contact")}
                    className="nav-link"
                  >
                    Contact
                  </button>
                </>
              ) : (
                <>
                  <Link href="/">
                    <span className="nav-link cursor-pointer">Home</span>
                  </Link>
                  <Link href="/#about">
                    <span className="nav-link cursor-pointer">About</span>
                  </Link>
                  <Link href="/projects">
                    <span
                      className={`nav-link cursor-pointer ${location === "/projects" ? "text-white" : ""}`}
                    >
                      Projects
                    </span>
                  </Link>
                  <Link href="/#services">
                    <span className="nav-link cursor-pointer">Services</span>
                  </Link>
                  <Link href="/#skills">
                    <span className="nav-link cursor-pointer">Core Skills</span>
                  </Link>
                  <Link href="/#contact">
                    <span className="nav-link cursor-pointer">Contact</span>
                  </Link>
                </>
              )}
              <a
                href="https://hashnode.com/@engrabm"
                className="nav-link"
                target="_blank"
                rel="noopener"
              >
                Blog
              </a>
              <a
                href="/prologware"
                className="inline-flex items-center px-4 py-2 rounded-lg bg-indigo-600 text-white text-sm font-medium hover:scale-105 hover:shadow-lg transition-all duration-300"
                target="_blank"
                rel="noopener"
              >
                <i className="fas fa-users mr-2"></i>
                Join Community
              </a>
            </div>

            {/* Mobile Menu Button - Only visible on mobile devices (hidden on desktop) */}
            <button
              className="mobile-menu-button block md:hidden text-gray-300 hover:text-white p-2 rounded-md transition-all duration-200 hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/20"
              onClick={toggleMenu}
              aria-expanded={isOpen}
              aria-label="Toggle navigation menu"
              type="button"
            >
              <svg
                className={`h-6 w-6 ${isOpen ? "hidden" : "block"}`}
                stroke="currentColor"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
              <svg
                className={`h-6 w-6 ${isOpen ? "block" : "hidden"}`}
                stroke="currentColor"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu - Only visible on mobile devices (hidden on desktop) */}
        {isOpen && (
          <div className="mobile-menu-container block md:hidden absolute top-full left-0 right-0 z-40 bg-[#171723]/98 backdrop-blur-md border-t border-white/10 shadow-xl">
            <div className="px-4 py-4 space-y-1 max-h-[calc(100vh-60px)] overflow-y-auto">
              {location === "/" ? (
                <>
                  <button
                    onClick={() => handleLinkClick("#home")}
                    className="flex items-center w-full px-4 py-3 text-left text-gray-300 hover:text-white hover:bg-white/5 rounded-lg transition-all duration-200 touch-manipulation"
                  >
                    <i className="fas fa-home w-5 mr-3 text-indigo-400"></i>
                    <span>Home</span>
                  </button>
                  <button
                    onClick={() => handleLinkClick("#about")}
                    className="flex items-center w-full px-4 py-3 text-left text-gray-300 hover:text-white hover:bg-white/5 rounded-lg transition-all duration-200 touch-manipulation"
                  >
                    <i className="fas fa-user w-5 mr-3 text-indigo-400"></i>
                    <span>About</span>
                  </button>
                  <button
                    onClick={() => handleLinkClick("#projects")}
                    className="flex items-center w-full px-4 py-3 text-left text-gray-300 hover:text-white hover:bg-white/5 rounded-lg transition-all duration-200 touch-manipulation"
                  >
                    <i className="fas fa-code w-5 mr-3 text-indigo-400"></i>
                    <span>Projects</span>
                  </button>
                  <button
                    onClick={() => handleLinkClick("#services")}
                    className="flex items-center w-full px-4 py-3 text-left text-gray-300 hover:text-white hover:bg-white/5 rounded-lg transition-all duration-200 touch-manipulation"
                  >
                    <i className="fas fa-cogs w-5 mr-3 text-indigo-400"></i>
                    <span>Services</span>
                  </button>
                  <button
                    onClick={() => handleLinkClick("#skills")}
                    className="flex items-center w-full px-4 py-3 text-left text-gray-300 hover:text-white hover:bg-white/5 rounded-lg transition-all duration-200 touch-manipulation"
                  >
                    <i className="fas fa-tools w-5 mr-3 text-indigo-400"></i>
                    <span>Core Skills</span>
                  </button>
                  <button
                    onClick={() => handleLinkClick("#contact")}
                    className="flex items-center w-full px-4 py-3 text-left text-gray-300 hover:text-white hover:bg-white/5 rounded-lg transition-all duration-200 touch-manipulation"
                  >
                    <i className="fas fa-envelope w-5 mr-3 text-indigo-400"></i>
                    <span>Contact</span>
                  </button>
                </>
              ) : (
                <>
                  <Link
                    href="/"
                    className="mobile-link"
                    onClick={() => setIsOpen(false)}
                  >
                    <i className="fas fa-home w-5"></i>
                    <span>Home</span>
                  </Link>
                  <Link
                    href="/#about"
                    className="mobile-link"
                    onClick={() => setIsOpen(false)}
                  >
                    <i className="fas fa-user w-5"></i>
                    <span>About</span>
                  </Link>
                  <Link
                    href="/projects"
                    className="mobile-link"
                    onClick={() => setIsOpen(false)}
                  >
                    <i className="fas fa-code w-5"></i>
                    <span>Projects</span>
                  </Link>
                  <Link
                    href="/#services"
                    className="mobile-link"
                    onClick={() => setIsOpen(false)}
                  >
                    <i className="fas fa-cogs w-5"></i>
                    <span>Services</span>
                  </Link>
                  <Link
                    href="/#skills"
                    className="mobile-link"
                    onClick={() => setIsOpen(false)}
                  >
                    <i className="fas fa-tools w-5"></i>
                    <span>Core Skills</span>
                  </Link>
                  <Link
                    href="/#contact"
                    className="mobile-link"
                    onClick={() => setIsOpen(false)}
                  >
                    <i className="fas fa-envelope w-5"></i>
                    <span>Contact</span>
                  </Link>
                </>
              )}

              {/* Divider */}
              <div className="border-t border-white/10 my-3"></div>

              <a
                href="https://abdulbasitmemon.hashnode.dev/"
                target="_blank"
                rel="noopener"
                className="flex items-center w-full px-4 py-3 text-left text-gray-300 hover:text-white hover:bg-white/5 rounded-lg transition-all duration-200 touch-manipulation"
                onClick={() => setIsOpen(false)}
              >
                <i className="fas fa-blog w-5 mr-3 text-indigo-400"></i>
                <span>Blog</span>
              </a>
              <a
                href="/prologware"
                target="_blank"
                rel="noopener"
                className="flex items-center justify-center w-full px-4 py-3 mt-3 text-center text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 transition-all duration-200 touch-manipulation"
                onClick={() => setIsOpen(false)}
              >
                <i className="fas fa-users mr-2"></i>
                Join Community
              </a>
            </div>
          </div>
        )}
      </nav>

      <style>{`
        .nav-link {
          color: rgb(209 213 219);
          font-size: 0.875rem;
          font-weight: 500;
          transition: color 0.2s;
          position: relative;
          padding: 0.5rem 0;
        }

        .nav-link:hover {
          color: white;
        }

        .nav-link::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 0;
          height: 2px;
          background-color: rgb(99 102 241);
          transition: width 0.2s;
        }

        .nav-link:hover::after {
          width: 100%;
        }

        .mobile-link {
          display: flex;
          align-items: center;
          padding: 0.5rem 1rem;
          color: rgb(209 213 219);
          font-size: 0.875rem;
          font-weight: 500;
          transition: all 0.2s;
          border-radius: 0.5rem;
          text-decoration: none;
        }

        .mobile-link:hover {
          color: white;
          background-color: rgba(255, 255, 255, 0.05);
        }
      `}</style>
    </>
  );
}
