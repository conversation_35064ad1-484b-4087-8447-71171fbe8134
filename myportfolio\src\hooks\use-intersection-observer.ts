import { useEffect, useRef, useState } from 'react'

interface UseIntersectionObserverOptions {
  threshold?: number | number[]
  root?: Element | null
  rootMargin?: string
  triggerOnce?: boolean
}

export function useIntersectionObserver(
  options: UseIntersectionObserverOptions = {}
) {
  const {
    threshold = 0.1,
    root = null,
    rootMargin = '0px',
    triggerOnce = true,
  } = options

  const [isIntersecting, setIsIntersecting] = useState(false)
  const [hasIntersected, setHasIntersected] = useState(false)
  const targetRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const target = targetRef.current
    if (!target) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        const isCurrentlyIntersecting = entry.isIntersecting
        setIsIntersecting(isCurrentlyIntersecting)

        if (isCurrentlyIntersecting && !hasIntersected) {
          setHasIntersected(true)
        }

        // If triggerOnce is false, allow re-triggering
        if (!triggerOnce && !isCurrentlyIntersecting && hasIntersected) {
          setHasIntersected(false)
        }
      },
      {
        threshold,
        root,
        rootMargin,
      }
    )

    observer.observe(target)

    return () => {
      observer.unobserve(target)
    }
  }, [threshold, root, rootMargin, triggerOnce, hasIntersected])

  return {
    ref: targetRef,
    isIntersecting,
    hasIntersected,
    isVisible: triggerOnce ? hasIntersected : isIntersecting,
  }
}

export function useMultipleIntersectionObserver(
  elementsCount: number,
  options: UseIntersectionObserverOptions = {}
) {
  const [visibleElements, setVisibleElements] = useState<boolean[]>(
    new Array(elementsCount).fill(false)
  )
  const refs = useRef<(HTMLElement | null)[]>([])

  useEffect(() => {
    const {
      threshold = 0.1,
      root = null,
      rootMargin = '0px',
      triggerOnce = true,
    } = options

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const index = refs.current.findIndex((ref) => ref === entry.target)
          if (index !== -1) {
            setVisibleElements((prev) => {
              const newState = [...prev]
              newState[index] = entry.isIntersecting
              return newState
            })
          }
        })
      },
      {
        threshold,
        root,
        rootMargin,
      }
    )

    refs.current.forEach((ref) => {
      if (ref) observer.observe(ref)
    })

    return () => {
      refs.current.forEach((ref) => {
        if (ref) observer.unobserve(ref)
      })
    }
  }, [elementsCount, options])

  const setRef = (index: number) => (element: HTMLElement | null) => {
    refs.current[index] = element
  }

  return {
    setRef,
    visibleElements,
  }
}

// Hook for staggered animations
export function useStaggeredAnimation(
  itemsCount: number,
  staggerDelay: number = 100
) {
  const [visibleItems, setVisibleItems] = useState<boolean[]>(
    new Array(itemsCount).fill(false)
  )
  const { ref, isVisible } = useIntersectionObserver({
    threshold: 0.1,
    triggerOnce: true,
  })

  useEffect(() => {
    if (isVisible) {
      // Stagger the animation of items
      for (let i = 0; i < itemsCount; i++) {
        setTimeout(() => {
          setVisibleItems((prev) => {
            const newState = [...prev]
            newState[i] = true
            return newState
          })
        }, i * staggerDelay)
      }
    }
  }, [isVisible, itemsCount, staggerDelay])

  return {
    containerRef: ref,
    visibleItems,
    isContainerVisible: isVisible,
  }
}
