import { useEffect, useRef, useState } from 'react';
import { useResponsive, useDeviceDetection } from '@/hooks/use-mobile';
import { throttle, getDeviceOptimizations } from '@/utils/performance';

interface SmoothScrollContainerProps {
  children: React.ReactNode;
  className?: string;
  enabled?: boolean;
  smoothness?: number; // 0.1 to 1, higher = smoother but more resource intensive
}

export function SmoothScrollContainer({
  children,
  className = '',
  enabled = true,
  smoothness = 0.1,
}: SmoothScrollContainerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [scrollY, setScrollY] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const animationRef = useRef<number>();
  const targetScrollY = useRef(0);
  const currentScrollY = useRef(0);
  
  const { isMobile } = useResponsive();
  const { isTouchDevice } = useDeviceDetection();
  const { shouldReduceAnimations, prefersReducedMotion } = getDeviceOptimizations();

  // Disable smooth scroll on mobile/touch devices or if user prefers reduced motion
  const shouldUseSmoothScroll = enabled && 
    !isMobile && 
    !isTouchDevice && 
    !shouldReduceAnimations && 
    !prefersReducedMotion;

  useEffect(() => {
    if (!shouldUseSmoothScroll || !containerRef.current || !contentRef.current) {
      return;
    }

    const container = containerRef.current;
    const content = contentRef.current;

    // Set up container styles for smooth scrolling
    container.style.position = 'fixed';
    container.style.top = '0';
    container.style.left = '0';
    container.style.width = '100%';
    container.style.height = '100%';
    container.style.overflow = 'hidden';
    container.style.pointerEvents = 'none';

    content.style.pointerEvents = 'auto';

    // Update body height to match content height
    const updateBodyHeight = () => {
      if (content) {
        document.body.style.height = `${content.scrollHeight}px`;
      }
    };

    // Smooth scroll animation
    const animate = () => {
      currentScrollY.current += (targetScrollY.current - currentScrollY.current) * smoothness;
      
      if (Math.abs(targetScrollY.current - currentScrollY.current) < 0.1) {
        currentScrollY.current = targetScrollY.current;
        setIsScrolling(false);
      } else {
        setIsScrolling(true);
      }

      if (content) {
        content.style.transform = `translateY(${-currentScrollY.current}px)`;
      }

      setScrollY(currentScrollY.current);
      animationRef.current = requestAnimationFrame(animate);
    };

    // Handle scroll events
    const handleScroll = throttle(() => {
      targetScrollY.current = window.pageYOffset;
    }, 16); // ~60fps

    // Handle resize events
    const handleResize = throttle(() => {
      updateBodyHeight();
    }, 100);

    // Initialize
    updateBodyHeight();
    animate();

    // Add event listeners
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleResize);

    // Observe content changes
    const resizeObserver = new ResizeObserver(updateBodyHeight);
    resizeObserver.observe(content);

    return () => {
      // Cleanup
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
      resizeObserver.disconnect();

      // Reset styles
      document.body.style.height = '';
      if (container) {
        container.style.position = '';
        container.style.top = '';
        container.style.left = '';
        container.style.width = '';
        container.style.height = '';
        container.style.overflow = '';
        container.style.pointerEvents = '';
      }
      if (content) {
        content.style.transform = '';
        content.style.pointerEvents = '';
      }
    };
  }, [shouldUseSmoothScroll, smoothness]);

  // If smooth scroll is disabled, render normally
  if (!shouldUseSmoothScroll) {
    return <div className={className}>{children}</div>;
  }

  return (
    <div ref={containerRef} className={className}>
      <div 
        ref={contentRef}
        className="will-change-transform"
        style={{
          transform: `translateY(${-scrollY}px)`,
        }}
      >
        {children}
      </div>
      

    </div>
  );
}

// Hook for accessing scroll position within smooth scroll context
export function useSmoothScrollPosition() {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = throttle(() => {
      setScrollY(window.pageYOffset);
    }, 16);

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return scrollY;
}

// Component for scroll-triggered animations
interface ScrollTriggerProps {
  children: React.ReactNode;
  threshold?: number;
  rootMargin?: string;
  className?: string;
  animationClass?: string;
}

export function ScrollTrigger({
  children,
  threshold = 0.1,
  rootMargin = '0px',
  className = '',
  animationClass = 'animate-fade-in-up',
}: ScrollTriggerProps) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(entry.target);
        }
      },
      { threshold, rootMargin }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold, rootMargin]);

  return (
    <div
      ref={ref}
      className={`${className} ${isVisible ? animationClass : 'opacity-0'}`}
    >
      {children}
    </div>
  );
}
