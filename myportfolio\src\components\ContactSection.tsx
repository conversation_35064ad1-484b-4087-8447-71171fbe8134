import React, { useState, useEffect, useRef } from "react";
import { CONTACT_INFO, SOCIAL_LINKS } from "../lib/constants";

export function ContactSection() {
  const [result, setResult] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);

  // Ensure form inputs are properly initialized
  useEffect(() => {
    // Force re-render of form inputs to ensure they're interactive
    const inputs = document.querySelectorAll('.contact-form input, .contact-form textarea');
    inputs.forEach((input) => {
      if (input instanceof HTMLInputElement || input instanceof HTMLTextAreaElement) {
        input.style.pointerEvents = 'auto';
        input.style.userSelect = 'text';
        (input.style as any).webkitUserSelect = 'text';
      }
    });
  }, []);

  const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);
    setResult("Sending....");

    const form = formRef.current;
    if (!form) {
      setResult("Form error. Please try again.");
      setIsSubmitting(false);
      return;
    }

    const formData = new FormData(form);

    // Basic form validation
    const name = formData.get("name") as string;
    const email = formData.get("email") as string;
    const subject = formData.get("subject") as string;
    const message = formData.get("message") as string;

    if (!name || !email || !subject || !message) {
      setResult("❌ Please fill in all required fields.");
      setIsSubmitting(false);
      return;
    }

    // Add Web3Forms access key
    formData.append("access_key", "d4e644bf-1892-44d2-94d8-9947f958c9aa");

    try {
      const response = await fetch("https://api.web3forms.com/submit", {
        method: "POST",
        body: formData
      });

      const data = await response.json();

      // Handle successful response
      if (data && data.success) {
        setResult("✅ Message sent successfully! I'll get back to you soon.");
        form.reset();
        setTimeout(() => setResult(""), 5000);
      } else {
        setResult(data?.message || "❌ Something went wrong. Please try again.");
      }
      setIsSubmitting(false);
    } catch (error) {
      console.error("Complete error details:", {
        error,
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });

      if (error instanceof TypeError && error.message.includes("fetch")) {
        setResult("❌ Connection error. Please check your internet connection.");
      } else {
        setResult(
          error instanceof Error
            ? `❌ ${error.message}`
            : "❌ An unexpected error occurred. Please try again.",
        );
      }
      setIsSubmitting(false);
    }
  };

  return (
    <section
      id="contact"
      className="py-16 md:py-24 scroll-section relative overflow-hidden"
    >
      {/* Unified background system matching site theme */}
      <div className="absolute inset-0 bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary"></div>

      {/* Unified animated background elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 md:w-96 md:h-96 bg-gradient-to-br from-indigo-500/25 via-purple-500/20 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-48 h-48 md:w-80 md:h-80 bg-gradient-to-tr from-blue-500/20 via-cyan-500/15 to-transparent rounded-full blur-3xl animate-pulse-slow2"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 md:w-48 md:h-48 bg-gradient-to-br from-emerald-500/15 via-teal-500/10 to-transparent rounded-full blur-2xl animate-hover-float"></div>

        {/* Additional floating particles */}
        <div className="absolute top-20 right-20 w-4 h-4 bg-indigo-400/40 rounded-full animate-bounce-slow"></div>
        <div className="absolute bottom-32 left-16 w-3 h-3 bg-purple-400/40 rounded-full animate-pulse-slow"></div>
        <div className="absolute top-40 left-20 w-2 h-2 bg-blue-400/40 rounded-full animate-hover-float"></div>
      </div>

      <div className="container relative z-10 mx-auto px-4">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-indigo-400 via-blue-400 to-purple-400 bg-clip-text text-transparent tracking-tight leading-tight hover:scale-105 transition-transform duration-300 cursor-default mb-4">
            Contact Me
          </h2>
          <p className="text-slate-400 text-lg max-w-2xl mx-auto">
            Ready to bring your ideas to life? Let's collaborate and create
            something amazing together.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Contact Information */}
          <div className="space-y-8">
            <div>
              <h3 className="text-2xl font-semibold text-text-primary mb-6">
                Get In Touch
              </h3>
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 glass-effect rounded-xl flex items-center justify-center">
                    <i className="fas fa-envelope text-primary-color"></i>
                  </div>
                  <div>
                    <div className="text-text-muted text-sm">Email</div>
                    <a
                      href={`mailto:${CONTACT_INFO.email}`}
                      className="text-text-primary hover:text-primary-color transition-colors cursor-pointer"
                    >
                      {CONTACT_INFO.email}
                    </a>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 glass-effect rounded-xl flex items-center justify-center">
                    <i className="fab fa-whatsapp text-green-500"></i>
                  </div>
                  <div>
                    <div className="text-text-muted text-sm">WhatsApp</div>
                    <a
                      href={CONTACT_INFO.whatsappLink}
                      className="text-text-primary hover:text-green-400 transition-colors"
                    >
                      {CONTACT_INFO.whatsapp}
                    </a>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 glass-effect rounded-xl flex items-center justify-center">
                    <i className="fas fa-map-marker-alt text-primary-color"></i>
                  </div>
                  <div>
                    <div className="text-text-muted text-sm">Location</div>
                    <div className="text-text-primary">
                      {CONTACT_INFO.location}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Social Links */}
            <div>
              <h4 className="text-lg font-semibold text-text-primary mb-4">
                Connect With Me
              </h4>
              <div className="flex gap-4">
                {SOCIAL_LINKS.map((social) => (
                  <a
                    key={social.name}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-12 h-12 glass-effect rounded-xl flex items-center justify-center hover-glow transition-all duration-300"
                  >
                    <i className={`${social.icon} text-primary-color`}></i>
                  </a>
                ))}
              </div>
            </div>
          </div>

          {/* Premium Contact Form */}
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-blue-500/20 rounded-3xl blur-xl"></div>
            <div className="relative glass-effect rounded-3xl p-8 md:p-10 border border-primary-color/30 backdrop-blur-xl bg-gradient-to-br from-bg-primary/90 via-bg-secondary/95 to-bg-primary/90 shadow-2xl">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-blue-500 rounded-t-3xl"></div>

              <div className="mb-8 text-center">
                <h3 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-indigo-400 via-purple-400 to-blue-400 bg-clip-text text-transparent mb-2">
                  Send a Message
                </h3>
                <p className="text-slate-400 text-sm">
                  Let's start a conversation
                </p>
              </div>

              <form ref={formRef} onSubmit={onSubmit} className="contact-form space-y-6">
                {/* Hidden fields for Web3Forms */}
                <input
                  type="hidden"
                  name="access_key"
                  value="d4e644bf-1892-44d2-94d8-9947f958c9aa"
                />
                <input
                  type="hidden"
                  name="from_name"
                  value="Abdul Basit Portfolio Contact"
                />
                <input
                  type="hidden"
                  name="_subject"
                  value="New Contact Message from Portfolio"
                />
                <input type="hidden" name="_captcha" value="false" />
                <input type="hidden" name="_template" value="table" />

                <div className="flex flex-col space-y-7">
                  <div className="group">
                    <label
                      htmlFor="name"
                      className="block text-base font-semibold text-blue-400 mb-3 tracking-wide flex items-center gap-2"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                      Full Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      className="block w-full px-6 py-4 bg-slate-800/90 border border-slate-600/50 rounded-2xl text-white placeholder:text-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-inner backdrop-blur-sm hover:border-blue-400/50 focus:bg-slate-800"
                      placeholder="Enter your full name"
                      required
                    />
                  </div>

                  <div className="group">
                    <label
                      htmlFor="email"
                      className="block text-base font-semibold text-blue-400 mb-3 tracking-wide flex items-center gap-2"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                        />
                      </svg>
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      className="block w-full px-6 py-4 bg-slate-800/90 border border-slate-600/50 rounded-2xl text-white placeholder:text-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-inner backdrop-blur-sm hover:border-blue-400/50 focus:bg-slate-800"
                      placeholder="Enter your email address"
                      required
                    />
                  </div>

                  <div className="group">
                    <label
                      htmlFor="subject"
                      className="block text-base font-semibold text-blue-400 mb-3 tracking-wide flex items-center gap-2"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"
                        />
                      </svg>
                      Subject
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      className="block w-full px-6 py-4 bg-slate-800/90 border border-slate-600/50 rounded-2xl text-white placeholder:text-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-inner backdrop-blur-sm hover:border-blue-400/50 focus:bg-slate-800"
                      placeholder="What's this about?"
                      required
                    />
                  </div>

                  <div className="group">
                    <label
                      htmlFor="message"
                      className="block text-base font-semibold text-blue-400 mb-3 tracking-wide flex items-center gap-2"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                        />
                      </svg>
                      Message
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      rows={5}
                      className="block w-full px-6 py-4 bg-slate-800/90 border border-slate-600/50 rounded-2xl text-white placeholder:text-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-inner backdrop-blur-sm hover:border-blue-400/50 focus:bg-slate-800 resize-none"
                      placeholder="Tell me about your project, ideas, or just say hello..."
                      required
                    ></textarea>
                  </div>

                  {/* Result message display */}
                  {result && (
                    <div
                      className={`text-center p-4 rounded-xl ${
                        result.includes("successfully")
                          ? "bg-green-500/20 text-green-400 border border-green-500/30"
                          : result.includes("Sending")
                            ? "bg-blue-500/20 text-blue-400 border border-blue-500/30"
                            : "bg-red-500/20 text-red-400 border border-red-500/30"
                      }`}
                    >
                      {result}
                    </div>
                  )}

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="relative premium-btn w-full py-5 px-8 text-lg font-bold flex items-center justify-center gap-3 shadow-2xl border-2 border-primary-color/50 rounded-2xl bg-gradient-to-r from-indigo-600/90 via-purple-600/90 to-blue-600/90 hover:from-indigo-500 hover:via-purple-500 hover:to-blue-500 transition-all duration-500 group overflow-hidden disabled:opacity-50 disabled:cursor-not-allowed hover:scale-[1.02] hover:shadow-xl hover:shadow-primary-color/25 backdrop-blur-sm"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    {isSubmitting ? (
                      <div className="flex items-center gap-3">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span className="z-10 relative text-white">
                          Sending Message...
                        </span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-3 z-10 relative">
                        <span className="text-white">Send Message</span>
                        <svg
                          className="w-5 h-5 text-white group-hover:translate-x-1 transition-transform duration-300"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                          />
                        </svg>
                      </div>
                    )}

                    <span className="btn-glow"></span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
