@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced base styles for better cross-device compatibility */
@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
    height: 100%;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    min-height: 100%;
    overflow-x: hidden;
  }

  #root {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  /* Improved touch targets for mobile and remove click outlines */
  button, a, input, textarea, select {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    outline: none !important;
    -webkit-focus-ring-color: transparent !important;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  /* Remove focus outlines on click but keep for keyboard navigation */
  button:focus:not(:focus-visible),
  a:focus:not(:focus-visible),
  input:focus:not(:focus-visible),
  textarea:focus:not(:focus-visible),
  select:focus:not(:focus-visible) {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Keep focus visible for keyboard users (accessibility) */
  button:focus-visible,
  a:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible {
    outline: 2px solid rgba(59, 130, 246, 0.5) !important;
    outline-offset: 2px !important;
    border-radius: 4px;
  }

  /* Better focus styles for accessibility */
  *:focus-visible {
    @apply outline-2 outline-offset-2 outline-ring;
  }
}

/* Enhanced responsive utilities */
@layer utilities {
  /* Smooth scrolling with hardware acceleration */
  .smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile-first responsive text sizes */
  .text-responsive-xs { @apply text-xs sm:text-sm; }
  .text-responsive-sm { @apply text-sm sm:text-base; }
  .text-responsive-base { @apply text-base sm:text-lg; }
  .text-responsive-lg { @apply text-lg sm:text-xl; }
  .text-responsive-xl { @apply text-xl sm:text-2xl; }
  .text-responsive-2xl { @apply text-2xl sm:text-3xl; }
  .text-responsive-3xl { @apply text-3xl sm:text-4xl; }
  .text-responsive-4xl { @apply text-4xl sm:text-5xl; }

  /* Responsive spacing utilities */
  .space-responsive-x { @apply space-x-2 sm:space-x-4 lg:space-x-6; }
  .space-responsive-y { @apply space-y-4 sm:space-y-6 lg:space-y-8; }
  .gap-responsive { @apply gap-4 sm:gap-6 lg:gap-8; }

  /* Mobile-optimized padding and margins */
  .p-responsive { @apply p-4 sm:p-6 lg:p-8; }
  .px-responsive { @apply px-4 sm:px-6 lg:px-8; }
  .py-responsive { @apply py-8 sm:py-12 lg:py-16; }
  .m-responsive { @apply m-4 sm:m-6 lg:m-8; }

  /* Container with responsive max-widths */
  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Glass morphism effect with better performance */
  .glass-effect {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    will-change: transform;
  }

  /* Hardware-accelerated animations */
  .animate-smooth {
    will-change: transform, opacity;
    transform: translateZ(0);
  }

  /* Enhanced hover effects with better performance */
  .hover-lift {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .hover-lift:hover {
    transform: translateY(-4px) translateZ(0);
  }

  /* Mobile-optimized touch interactions */
  .touch-action-pan-y { touch-action: pan-y; }
  .touch-action-pan-x { touch-action: pan-x; }
  .touch-action-manipulation { touch-action: manipulation; }

  /* Responsive grid utilities */
  .grid-responsive-1 { @apply grid-cols-1; }
  .grid-responsive-2 { @apply grid-cols-1 sm:grid-cols-2; }
  .grid-responsive-3 { @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3; }
  .grid-responsive-4 { @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-4; }

  /* Improved button styles for all devices */
  .btn-responsive {
    @apply px-4 py-2 sm:px-6 sm:py-3 text-sm sm:text-base;
    @apply min-h-[44px] min-w-[44px]; /* Minimum touch target size */
    @apply transition-all duration-200 ease-in-out;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  /* Loading states with smooth animations */
  .loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Intersection observer fade-in animation */
  .fade-in-up {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .fade-in-up.visible {
    opacity: 1;
    transform: translateY(0);
  }

  /* Mobile-specific optimizations */
  @media (max-width: 768px) {
    .mobile-optimized {
      -webkit-overflow-scrolling: touch;
      scroll-behavior: smooth;
    }

    /* Larger touch targets for mobile */
    .mobile-touch-target {
      min-height: 44px;
      min-width: 44px;
    }

    /* Prevent zoom on input focus */
    input, textarea, select {
      font-size: 16px;
    }

    /* Better mobile typography */
    .mobile-text-scale {
      font-size: clamp(0.875rem, 2.5vw, 1rem);
    }

    .mobile-heading-scale {
      font-size: clamp(1.5rem, 5vw, 2.5rem);
    }
  }

  /* High DPI display optimizations */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .high-dpi-optimized {
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* Dark mode optimizations */
  @media (prefers-color-scheme: dark) {
    .auto-dark-mode {
      filter: brightness(0.8) contrast(1.2);
    }
  }

  /* Print styles */
  @media print {
    .no-print { display: none !important; }
    .print-only { display: block !important; }

    * {
      background: transparent !important;
      color: black !important;
      box-shadow: none !important;
      text-shadow: none !important;
    }
  }

  /* Contact form input fixes */
  .contact-form input,
  .contact-form textarea {
    background-color: rgba(30, 41, 59, 0.9) !important;
    color: white !important;
    border: 1px solid rgba(100, 116, 139, 0.5) !important;
    position: relative !important;
    z-index: 10 !important;
    pointer-events: auto !important;
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    cursor: text !important;
    outline: none !important;
    -webkit-tap-highlight-color: transparent !important;
  }

  .contact-form input:focus,
  .contact-form textarea:focus {
    background-color: rgba(30, 41, 59, 1) !important;
    border-color: rgb(59, 130, 246) !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5) !important;
    -webkit-tap-highlight-color: transparent !important;
  }

  .contact-form input:hover,
  .contact-form textarea:hover {
    border-color: rgba(59, 130, 246, 0.5) !important;
  }

  .contact-form input::placeholder,
  .contact-form textarea::placeholder {
    color: rgba(148, 163, 184, 0.8) !important;
    opacity: 1 !important;
  }

  /* Ensure form labels are visible */
  .contact-form label {
    color: rgb(96, 165, 250) !important;
    font-weight: 600 !important;
  }

  /* Remove any interfering overlays */
  .contact-form .group::before,
  .contact-form .group::after {
    display: none !important;
  }

  /* Mobile Navigation Enhancements - Hidden on Desktop */
  .mobile-menu-button {
    min-height: 44px !important;
    min-width: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: transparent !important;
  }

  .mobile-menu-container {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 40 !important;
    background: rgba(23, 23, 35, 0.98) !important;
    backdrop-filter: blur(12px) !important;
    -webkit-backdrop-filter: blur(12px) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;
    animation: slideDown 0.2s ease-out !important;
  }

  /* Ensure mobile elements are completely hidden on desktop */
  @media (min-width: 768px) {
    .mobile-menu-button,
    .mobile-menu-container {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      pointer-events: none !important;
    }
  }

  /* Remove outlines from all navigation elements */
  .nav-link,
  .mobile-link,
  .navbar a,
  .navbar button,
  nav a,
  nav button {
    outline: none !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-focus-ring-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
  }

  .nav-link:focus,
  .mobile-link:focus,
  .navbar a:focus,
  .navbar button:focus,
  nav a:focus,
  nav button:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  .nav-link:active,
  .mobile-link:active,
  .navbar a:active,
  .navbar button:active,
  nav a:active,
  nav button:active {
    outline: none !important;
    box-shadow: none !important;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Mobile menu links */
  .mobile-menu-container button,
  .mobile-menu-container a {
    min-height: 48px !important;
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: transparent !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
  }

  .mobile-menu-container button:active,
  .mobile-menu-container a:active {
    background-color: rgba(255, 255, 255, 0.1) !important;
    transform: scale(0.98) !important;
    outline: none !important;
  }

  .mobile-menu-container button:focus,
  .mobile-menu-container a:focus {
    outline: none !important;
    box-shadow: none !important;
  }
}

@keyframes wave {
  0% { transform: rotate(0deg); }
  10% { transform: rotate(14deg); }
  20% { transform: rotate(-8deg); }
  30% { transform: rotate(14deg); }
  40% { transform: rotate(-4deg); }
  50% { transform: rotate(10deg); }
  60% { transform: rotate(0deg); }
  100% { transform: rotate(0deg); }
}

.animate-wave {
  animation: wave 1.2s cubic-bezier(.36,.07,.19,.97) both;
  transform-origin: 70% 70%;
}

@keyframes pulse-slow {
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.08); }
}

.animate-pulse-slow {
  animation: pulse-slow 6s ease-in-out infinite;
}

@keyframes pulse-slow2 {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.12); }
}

.animate-pulse-slow2 {
  animation: pulse-slow2 8s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-30px); }
}

.animate-float {
  animation: float 7s ease-in-out infinite;
}

@keyframes float-gentle {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(5deg); }
}

.animate-float-gentle {
  animation: float-gentle 10s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 4s ease infinite;
}

:root {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(247, 84%, 63%);
  --primary-foreground: hsl(248, 100%, 98%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;

  /* Premium Portfolio Theme */
  --bg-primary: hsl(222, 84%, 4.9%);
  --bg-secondary: hsl(217, 32.6%, 17.5%);
  --bg-tertiary: hsl(215, 25%, 26.7%);
  --primary-color: hsl(247, 84%, 63%);
  --primary-light: hsl(249, 85%, 72%);
  --primary-dark: hsl(243, 75%, 59%);
  --secondary-color: hsl(187, 95%, 43%);
  --secondary-light: hsl(186, 86%, 70%);
  --accent-color: hsl(263, 70%, 65%);
  --accent-light: hsl(267, 84%, 73%);
  --text-primary: hsl(210, 40%, 98%);
  --text-secondary: hsl(210, 40%, 88%);
  --text-muted: hsl(215, 20.2%, 65.1%);
  --border-color: hsl(247, 84%, 63%, 0.2);
  --card-bg: hsl(217, 32.6%, 17.5%, 0.8);
  --glass-bg: hsl(222, 84%, 4.9%, 0.9);
  --shadow-primary: hsl(247, 84%, 63%, 0.25);
  --shadow-secondary: hsl(187, 95%, 43%, 0.15);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(247, 84%, 63%);
  --primary-foreground: hsl(248, 100%, 98%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%);
    color: var(--text-primary);
    overflow-x: hidden;
    scroll-behavior: smooth;
    font-display: swap;
    contain: layout style paint;
  }

  /* Optimized root element for performance and smooth scrolling */
  html {
    scroll-behavior: smooth !important;
    scroll-padding-top: 80px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    -webkit-tap-highlight-color: transparent;
  }

  /* Hardware acceleration for better performance */
  html, body {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }
}

@layer components {
  .glass-effect {
    background: var(--glass-bg);
    backdrop-filter: blur(24px) saturate(180%);
    border: 1px solid var(--border-color);
  }

  .glass-morphism {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient 8s ease infinite;
  }

  /* Premium Section Headings */
  .section-heading {
    position: relative;
    display: inline-block;
    font-weight: 700;
    font-size: clamp(2rem, 5vw, 3rem);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
  }

  .section-heading::after {
    content: '';
    position: absolute;
    width: 60px;
    height: 4px;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
  }

  @media (max-width: 768px) {
    .section-heading {
      font-size: clamp(1.75rem, 4vw, 2.5rem);
    }

    .section-heading::after {
      width: 40px;
      height: 3px;
      bottom: -12px;
    }
  }

  .hover-glow:hover {
    box-shadow: 0 0 30px var(--shadow-primary);
    transform: translateY(-2px);
  }

  .nav-underline {
    position: relative;
  }

  .nav-underline::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
  }

  .nav-underline:hover::after {
    width: 100%;
  }

  /* Premium Button Styling */
  .premium-btn {
    position: relative;
    overflow: hidden;
    color: white;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .premium-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
  }

  .btn-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .premium-btn:hover .btn-glow {
    left: 100%;
  }
}

@layer utilities {
  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-pulse-slow2 {
    animation: pulse 6s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-float {
    animation: float 8s ease-in-out infinite;
  }

  .animate-gradient {
    animation: gradient 8s ease infinite;
  }

  /* Premium Heading Responsive Sizing - Optimized for better visual hierarchy */
  .title-responsive {
    font-size: clamp(2.2rem, 6.5vw, 4.5rem);
    line-height: 1.1;
  }

  .heading-responsive {
    font-size: clamp(1.2rem, 3.2vw, 2rem);
    line-height: 1.2;
  }

  .text-responsive {
    font-size: clamp(0.9rem, 2.2vw, 1.1rem);
    line-height: 1.6;
  }

  /* Performance optimizations */
  * {
    scroll-behavior: smooth;
  }

  /* Enhanced smooth scrolling - removed duplicate */

  /* Enhanced Image optimization and loading */
  img {
    loading: lazy;
    content-visibility: auto;
    image-rendering: crisp-edges;
    image-rendering: -webkit-optimize-contrast;
    transform: translateZ(0);
    will-change: transform;
  }

  /* Critical images load immediately */
  img[loading="eager"] {
    loading: eager;
    content-visibility: visible;
  }

  /* Critical images should load immediately */
  .critical-image {
    loading: eager !important;
    content-visibility: visible !important;
  }

  /* Lazy loading animations */
  .lazy-load {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }

  /* Premium Interactive Elements */
  .interactive-feature {
    position: relative;
    cursor: pointer;
    will-change: transform;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .interactive-feature:hover {
    transform: translateY(-8px) scale(1.02);
  }

  .interactive-feature::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  .interactive-feature:hover::before {
    opacity: 1;
  }

  /* Enhanced Button Animations */
  .premium-cta {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    color: white;
    cursor: pointer;
    will-change: transform, box-shadow;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .premium-cta:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 20px 40px rgba(99, 102, 241, 0.4);
  }

  .premium-cta::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
  }

  .premium-cta:hover::after {
    left: 100%;
  }

  /* Video Enhancement Effects */
  .video-container {
    position: relative;
    overflow: hidden;
    border-radius: 1.5rem;
    transition: all 0.5s ease;
  }

  .video-container:hover {
    transform: scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  }

  .video-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  .video-container:hover .video-overlay {
    opacity: 1;
  }

  /* Floating Animation Keyframes */
  @keyframes float-gentle {
    0%, 100% { 
      transform: translateY(0px) rotate(0deg);
    }
    33% { 
      transform: translateY(-10px) rotate(1deg);
    }
    66% { 
      transform: translateY(-5px) rotate(-1deg);
    }
  }

  .animate-float-gentle {
    animation: float-gentle 6s ease-in-out infinite;
  }

  /* Glow Pulse Effect */
  @keyframes glow-pulse {
    0%, 100% {
      box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
    }
    50% {
      box-shadow: 0 0 40px rgba(99, 102, 241, 0.6), 0 0 60px rgba(139, 92, 246, 0.3);
    }
  }

  .animate-glow-pulse {
    animation: glow-pulse 3s ease-in-out infinite;
  }

  /* Enhanced Gradient Animation */
  @keyframes gradient-shift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .animate-gradient-shift {
    background-size: 200% 200%;
    animation: gradient-shift 8s ease infinite;
  }

  /* Morphism Glass Effect Enhancement */
  .glass-morphism {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .glass-morphism:hover {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
  }

  /* Ultra-Premium Interactive Elements */
  .premium-interactive {
    position: relative;
    overflow: hidden;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  }

  .premium-interactive::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.8s ease;
  }

  .premium-interactive:hover::before {
    left: 100%;
  }

  /* Advanced Particle Effect */
  @keyframes particle-float {
    0%, 100% { 
      transform: translateY(0) rotate(0deg);
      opacity: 0.3;
    }
    25% { 
      transform: translateY(-20px) rotate(90deg);
      opacity: 0.7;
    }
    50% { 
      transform: translateY(-40px) rotate(180deg);
      opacity: 1;
    }
    75% { 
      transform: translateY(-20px) rotate(270deg);
      opacity: 0.7;
    }
  }

  .animate-particle {
    animation: particle-float 8s ease-in-out infinite;
  }

  /* Mouse Follower Effect */
  .mouse-follower {
    pointer-events: none;
    transition: all 0.1s ease;
  }

  /* Sophisticated Hover States */
  .premium-hover {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .premium-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
      0 20px 40px rgba(99, 102, 241, 0.3),
      0 10px 20px rgba(139, 92, 246, 0.2),
      0 5px 10px rgba(59, 130, 246, 0.1);
  }

  /* Advanced Text Gradient with Animation */
  .text-gradient-animate {
    background: linear-gradient(
      45deg,
      var(--primary-color),
      var(--secondary-color),
      var(--accent-color),
      var(--primary-color)
    );
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 6s ease infinite;
  }

  /* Ripple Effect for Buttons */
  .ripple {
    position: relative;
    overflow: hidden;
  }

  .ripple::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  .ripple:active::after {
    width: 300px;
    height: 300px;
  }

  /* Enhanced Shadow Layers */
  .shadow-layers {
    box-shadow: 
      0 1px 3px rgba(0, 0, 0, 0.12),
      0 1px 2px rgba(0, 0, 0, 0.24),
      0 8px 32px rgba(99, 102, 241, 0.15);
  }

  .shadow-layers:hover {
    box-shadow: 
      0 14px 28px rgba(0, 0, 0, 0.25),
      0 10px 10px rgba(0, 0, 0, 0.22),
      0 15px 50px rgba(99, 102, 241, 0.3),
      0 8px 25px rgba(139, 92, 246, 0.2);
  }

  /* Staggered Animation System */
  .stagger-1 { animation-delay: 0.1s; }
  .stagger-2 { animation-delay: 0.2s; }
  .stagger-3 { animation-delay: 0.3s; }
  .stagger-4 { animation-delay: 0.4s; }
  .stagger-5 { animation-delay: 0.5s; }
  .stagger-6 { animation-delay: 0.6s; }

  /* Advanced Backdrop Effects */
  .backdrop-enhanced {
    backdrop-filter: blur(40px) saturate(200%) brightness(110%);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
  }

  /* Micro-interactions */
  .micro-bounce {
    transition: transform 0.2s ease;
  }

  .micro-bounce:hover {
    transform: scale(1.05);
  }

  .micro-bounce:active {
    transform: scale(0.98);
  }

  /* Performance Optimized Animations */
  .will-change-transform {
    will-change: transform;
  }

  .will-change-opacity {
    will-change: opacity;
  }

  /* Custom Scrollbar for Premium Feel */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, var(--secondary-color), var(--accent-color));
  }

  .lazy-load.loaded {
    opacity: 1;
    transform: translateY(0);
  }

  /* Optimized scrolling with momentum and performance */
  .smooth-scroll {
    scroll-behavior: smooth;
    scroll-snap-type: y mandatory;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  .scroll-section {
    scroll-snap-align: start;
    scroll-snap-stop: normal;
    contain: layout style paint;
  }

  /* Optimized viewport transitions */
  @media (prefers-reduced-motion: no-preference) {
    .scroll-section {
      view-transition-name: auto;
    }
  }

  /* Performance: Enhanced GPU acceleration and optimization */
  .animate-pulse-slow,
  .animate-pulse-slow2,
  .animate-float,
  .hover-glow,
  .premium-btn {
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
    transform: translateZ(0);
    contain: layout style paint;
    isolation: isolate;
  }

  /* Remove will-change after animation to free GPU memory */
  .animate-pulse-slow:not(:hover),
  .animate-pulse-slow2:not(:hover),
  .animate-float:not(:hover) {
    will-change: auto;
  }

  /* Enhanced responsive optimization with performance focus */
  @media (max-width: 640px) {
    .container {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .space-y-8 > * + * {
      margin-top: 1.5rem;
    }

    .space-y-6 > * + * {
      margin-top: 1rem;
    }

    /* Reduce animation complexity on mobile for performance */
    .animate-pulse-slow,
    .animate-pulse-slow2,
    .animate-float {
      animation-duration: 8s;
      animation-timing-function: ease-in-out;
    }

    /* Optimize touch interactions */
    .touch-target {
      min-height: 44px;
      min-width: 44px;
      touch-action: manipulation;
    }
  }

  /* High-performance mobile optimizations */
  @media (max-width: 768px) {
    /* Disable expensive effects on smaller screens */
    .blur-3xl, .blur-2xl {
      filter: blur(8px);
    }

    /* Simplify gradients for better performance */
    .bg-gradient-to-br,
    .bg-gradient-to-tr {
      background-attachment: local;
    }
  }

  /* Loading states */
  .loading-skeleton {
    background: linear-gradient(90deg, 
      var(--bg-secondary) 25%, 
      var(--bg-tertiary) 50%, 
      var(--bg-secondary) 75%
    );
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  @keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }

  /* Performance optimization utilities */
  .will-change-transform {
    will-change: transform;
  }

  .will-change-auto {
    will-change: auto;
  }

  .contain-layout {
    contain: layout;
  }

  .contain-paint {
    contain: paint;
  }

  .contain-strict {
    contain: strict;
  }

  /* Intersection observer optimization */
  .lazy-section {
    content-visibility: auto;
    contain-intrinsic-size: 800px;
  }

  /* Smooth transitions with performance focus */
  .transition-optimized {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity;
  }

  .transition-optimized:not(:hover):not(:focus) {
    will-change: auto;
  }

  /* Animate in effect for intersection observer - only for specific sections, not hero */
  .animate-in {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }

  [data-animate]:not(#home [data-animate]) {
    opacity: 0;
    transform: translateY(20px);
  }

  /* Hero section should always be visible */
  #home [data-animate] {
    opacity: 1;
    transform: translateY(0);
  }

  /* Optimized focus states */
  *:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }

  /* Performance optimized hover states */
  .hover-lift {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
  }

  .hover-lift:hover {
    transform: translateY(-4px) translateZ(0);
  }

  .hover-lift:not(:hover) {
    will-change: auto;
  }

  /* Reduce motion for accessibility */
The provided changes are redundant as the "old_str" and "new_str" are identical. Therefore, no changes are applied to the original code.```text
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* Touch targets for mobile */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Navigation Background Enhancement */
  .navbar {
    background: rgba(23, 23, 35, 0.98) !important;
    backdrop-filter: blur(12px) saturate(180%);
    -webkit-backdrop-filter: blur(12px) saturate(180%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.12);
    box-shadow: 0 1px 20px rgba(0, 0, 0, 0.15);
    min-height: auto !important;
    height: auto !important;
  }

  /* Mobile Navigation Improvements */
  @media (max-width: 768px) {
    .navbar {
      height: auto !important;
      min-height: 56px !important;
      max-height: 56px !important;
      background: rgba(23, 23, 35, 0.98) !important;
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 50 !important;
    }

    .navbar .max-w-7xl > div {
      height: 56px !important;
      max-height: 56px !important;
    }

    .mobile-menu-button {
      min-height: 44px !important;
      min-width: 44px !important;
    }

    .mobile-menu-container {
      max-height: calc(100vh - 56px) !important;
      overflow-y: auto !important;
      -webkit-overflow-scrolling: touch !important;
    }

    /* Ensure mobile menu is always visible when open */
    .mobile-menu-container button,
    .mobile-menu-container a {
      display: flex !important;
      align-items: center !important;
      width: 100% !important;
      min-height: 48px !important;
      padding: 12px 16px !important;
      font-size: 0.875rem !important;
      font-weight: 500 !important;
      color: rgb(209, 213, 219) !important;
      text-decoration: none !important;
      border: none !important;
      background: transparent !important;
      border-radius: 8px !important;
      transition: all 0.2s ease !important;
      cursor: pointer !important;
      touch-action: manipulation !important;
    }

    .mobile-menu-container button:hover,
    .mobile-menu-container a:hover {
      color: white !important;
      background-color: rgba(255, 255, 255, 0.05) !important;
    }

    .mobile-menu-container button:active,
    .mobile-menu-container a:active {
      background-color: rgba(255, 255, 255, 0.1) !important;
      transform: scale(0.98) !important;
    }
  }

  /* Small screens - extra compact */
  @media (max-width: 640px) {
    .navbar {
      min-height: 52px !important;
      max-height: 52px !important;
    }

    .navbar .max-w-7xl > div {
      height: 52px !important;
      max-height: 52px !important;
      padding: 0 1rem;
    }

    .navbar .text-lg {
      font-size: 1rem;
    }

    .mobile-menu-container {
      max-height: calc(100vh - 52px) !important;
    }

    .mobile-menu-container button,
    .mobile-menu-container a {
      min-height: 44px !important;
      padding: 10px 14px !important;
      font-size: 0.8rem !important;
    }
  }

  /* Extra small screens - ultra compact */
  @media (max-width: 480px) {
    .navbar {
      min-height: 48px !important;
      max-height: 48px !important;
    }

    .navbar .max-w-7xl > div {
      height: 48px !important;
      max-height: 48px !important;
      padding: 0 0.875rem;
    }

    .navbar .text-lg {
      font-size: 0.9rem;
    }

    .mobile-menu-container {
      max-height: calc(100vh - 48px) !important;
      padding: 0.5rem;
    }

    .mobile-menu-container button,
    .mobile-menu-container a {
      min-height: 42px !important;
      padding: 8px 12px !important;
      font-size: 0.75rem !important;
    }

    .mobile-menu-button {
      min-height: 40px !important;
      min-width: 40px !important;
    }
  }

  /* Global outline removal for all clickable elements */
  * {
    -webkit-tap-highlight-color: transparent !important;
  }

  *:focus {
    outline: none !important;
    -webkit-focus-ring-color: transparent !important;
  }

  *:active {
    outline: none !important;
  }

  /* Remove outlines from specific interactive elements */
  button,
  a,
  input[type="button"],
  input[type="submit"],
  input[type="reset"],
  .btn,
  .button,
  .link,
  [role="button"],
  [tabindex] {
    outline: none !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-focus-ring-color: transparent !important;
    box-shadow: none !important;
  }

  button:focus,
  a:focus,
  input[type="button"]:focus,
  input[type="submit"]:focus,
  input[type="reset"]:focus,
  .btn:focus,
  .button:focus,
  .link:focus,
  [role="button"]:focus,
  [tabindex]:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  button:active,
  a:active,
  input[type="button"]:active,
  input[type="submit"]:active,
  input[type="reset"]:active,
  .btn:active,
  .button:active,
  .link:active,
  [role="button"]:active,
  [tabindex]:active {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Webkit specific outline removal */
  input,
  textarea,
  select,
  button,
  a {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-focus-ring-color: transparent !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
  }

  /* Allow text selection for input fields */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="search"],
  input[type="tel"],
  input[type="url"],
  textarea {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
  }

  /* Remove blue highlight on mobile Safari */
  * {
    -webkit-tap-highlight-color: rgba(0,0,0,0) !important;
    -webkit-tap-highlight-color: transparent !important;
  }

  /* Animation enhancements */
  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }

  .animate-hover-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
    animation-delay: 0.4s;
    opacity: 0;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Premium interactive effects */
  .premium-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .premium-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 40px rgba(99, 102, 241, 0.3);
  }

  .btn-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .premium-btn:hover .btn-glow {
    left: 100%;
  }

  .animate-wave {
    animation: wave 1.2s cubic-bezier(.36,.07,.19,.97) both;
    transform-origin: 70% 70%;
  }

  /* Optimized Premium Button System */
  .ultra-premium-button {
    position: relative;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
    background-size: 200% 200%;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3);
    will-change: transform, box-shadow;
  }

  .ultra-premium-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
    z-index: 1;
  }

  .ultra-premium-button:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 30px rgba(99, 102, 241, 0.4);
    background-position: 100% 0;
  }

  .ultra-premium-button:hover::after {
    left: 100%;
  }

  .ultra-premium-button:active {
    transform: translateY(0) scale(0.98);
  }

  /* Optimized Glass Button */
  .glass-premium-button {
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.2);
    will-change: transform, border-color;
  }

  .glass-premium-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: inherit;
    z-index: 1;
  }

  .glass-premium-button:hover {
    transform: translateY(-2px) scale(1.02);
    border-color: rgba(99, 102, 241, 0.4);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.3);
  }

  .glass-premium-button:hover::before {
    opacity: 1;
  }

  /* Simplified Pulse Effect */
  .pulse-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 1px solid rgba(99, 102, 241, 0.4);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: simple-pulse 2s ease-in-out infinite;
    pointer-events: none;
  }

  .pulse-ring:nth-child(2) {
    animation-delay: 0.7s;
    border-color: rgba(139, 92, 246, 0.3);
  }

  .pulse-ring:nth-child(3) {
    display: none; /* Remove for performance */
  }

  /* Optimized Service Card */
  .premium-service-card-ultra {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, box-shadow;
  }

  .premium-service-card-ultra::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(139, 92, 246, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: inherit;
    z-index: 1;
  }

  .premium-service-card-ultra:hover {
    transform: translateY(-4px) scale(1.01);
    border-color: rgba(99, 102, 241, 0.3);
    box-shadow: 0 10px 30px rgba(99, 102, 241, 0.2);
  }

  .premium-service-card-ultra:hover::before {
    opacity: 1;
  }

  /* Optimized Keyframe Animations */
  @keyframes simple-pulse {
    0%, 100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.6;
    }
    50% {
      transform: translate(-50%, -50%) scale(1.5);
      opacity: 0;
    }
  }

  /* Smooth scrolling optimization */
  html {
    scroll-behavior: smooth;
    scroll-padding-top: 2rem;
  }

  /* Performance optimizations */
  * {
    box-sizing: border-box;
  }

  /* Reduce animations on mobile for performance */
  @media (max-width: 768px) {
    .ultra-premium-button,
    .glass-premium-button,
    .premium-service-card-ultra {
      transition-duration: 0.2s;
    }

    .pulse-ring {
      display: none;
    }

    .ultra-premium-button:hover,
    .glass-premium-button:hover,
    .premium-service-card-ultra:hover {
      transform: translateY(-1px) scale(1.01);
    }
  }

@keyframes gradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translate(-50%, -50%) translateY(0px) rotate(0deg);
  }
  50% {
    transform: translate(-50%, -50%) translateY(-20px) rotate(5deg);
  }
}

@keyframes wave {
  0% { transform: rotate(0deg); }
  10% { transform: rotate(14deg); }
  20% { transform: rotate(-8deg); }
  30% { transform: rotate(14deg); }
  40% { transform: rotate(-4deg); }
  50% { transform: rotate(10deg); }
  60% { transform: rotate(0deg); }
  100% { transform: rotate(0deg); }
}

/* Background Animation */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
}

.bg-animation::before,
.bg-animation::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  opacity: 0.15;
  will-change: transform;
}

.bg-animation::before {
  width: min(400px, 50vw);
  height: min(400px, 50vw);
  background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
  animation: move-glow-1 20s infinite alternate ease-in-out;
  top: 10%;
  left: 10%;
}

.bg-animation::after {
  width: min(300px, 40vw);
  height: min(300px, 40vw);
  background: radial-gradient(circle, var(--secondary-color) 0%, transparent 70%);
  animation: move-glow-2 25s infinite alternate-reverse ease-in-out;
  bottom: 10%;
  right: 10%;
}

@keyframes move-glow-1 {
  0% { transform: translate(0, 0) scale(1); }
  50% { transform: translate(30vw, 20vh) scale(1.2); }
  100% { transform: translate(60vw, 40vh) scale(0.8); }
}

@keyframes move-glow-2 {
  0% { transform: translate(0, 0) scale(0.8); }
  50% { transform: translate(-20vw, -30vh) scale(1.1); }
  100% { transform: translate(-40vw, -10vh) scale(1); }
}

/* Additional Animations */
@keyframes pulse-slow2 {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes bounce-slow {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes hover-float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-pulse-slow2 {
  animation: pulse-slow2 4s ease-in-out infinite;
}

.animate-bounce-slow {
  animation: bounce-slow 3s ease-in-out infinite;
}

.animate-hover-float {
  animation: hover-float 2s ease-in-out infinite;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-light);
}
}